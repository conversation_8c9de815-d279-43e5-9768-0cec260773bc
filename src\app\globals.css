/* Smooth scrolling for anchor nav */
html {
  scroll-behavior: smooth;
  scroll-padding-top: 80px;
}

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  html { scroll-behavior: auto; }
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.5rem;
}
/* Brand palette based on logo */
:root {
  --brand-blue: #185AA5;
  --brand-green: #1BA777;
  --brand-orange: #DD7531;
  --brand-red: #C7133B;
}



/* Light/Dark theme tokens */
:root {
  --background: #ffffff;
  --foreground: #0a0a0a;
}

.dark {
  --background: #0a0a0a;
  --foreground: #ededed;
}

html, body {
  height: 100%;
}

body {
  background: var(--background);
  color: var(--foreground);
}

/* Simple container utility for consistent width */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* Provide some offset for in-page anchors */
section[id] { scroll-margin-top: 100px; }

/* Enhanced focus styles for accessibility */
*:focus-visible {
  outline: 2px solid var(--brand-blue);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background);
}

::-webkit-scrollbar-thumb {
  background: color-mix(in oklab, var(--brand-blue) 30%, transparent);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: color-mix(in oklab, var(--brand-blue) 50%, transparent);
}

/* Navigation styling */
nav a {
  position: relative;
  transition: all 0.2s ease;
}

nav a:hover {
  transform: translateY(-1px);
}

@media (min-width: 1024px) {
  .container {
    max-width: 1000px;
  }
}

